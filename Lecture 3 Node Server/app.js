const http = require("http");

const server = http.createServer((req, res) => {
	console.log(req.url, req.method, req.headers);
	res.setHeader("Content-Type", "text/html");
	res.write("<html>");
	res.write("<head><title> <PERSON> Ram </title></head>");
	res.write("<body><h1>Ram Ram</h1></body>");
	res.write("</html>");
	res.end();
	// process.exit();
});

const PORT = 3000;
server.listen(PORT, () => {
	console.log(`Server running on address http://127.0.0.1:${PORT}`);
});
