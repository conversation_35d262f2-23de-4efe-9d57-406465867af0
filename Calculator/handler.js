const userRequestHandler = (req, res) => {
	console.log(req.url, req.method);
	if (req.url === "/") {
		res.setHeader("Content-Type", "text/html");
		res.write(`
      <html>
        <head><title>Calculator</title></head>
        <body>
        <h1>Welcome to Calculator</h1>
          <a href = "/calculator">Go to calculator</a>
        </body>
      </html>
        `);
		return res.end();
	} else if (req.url.toLowerCase() === "/calculator") {
		res.setHeader("Content-Type", "text/html");
		res.write(`
      <html>
        <head>
          <title>Calculator</title>
        </head>
        <body>
          <h1>Calculator</h1>
          <form action = "/calculate-result" method = 'POST'>
            <input type = "number" name='num1' placeholder = 'Enter first number'>
            <input type = 'number' name = 'num2' placeholder = 'Enter second number'>
            <input type = 'submit' value = 'sum'>
          </form>
        </body>
      </html>
      `);
		return res.end();
	}
};

exports.userRequestHandler = userRequestHandler;
